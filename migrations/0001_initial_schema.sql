-- Initial database schema for the blog site
-- This creates the basic tables needed for the blog functionality

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    bio TEXT,
    avatar_url TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Posts table
CREATE TABLE IF NOT EXISTS posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft',
    post_type TEXT NOT NULL DEFAULT 'post',
    featured_image_url TEXT,
    meta_title TEXT,
    meta_description TEXT,
    language TEXT NOT NULL DEFAULT 'hi',
    view_count INTEGER NOT NULL DEFAULT 0,
    comment_count INTEGER NOT NULL DEFAULT 0,
    published_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id)
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_id INTEGER,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id)
);

-- Post categories junction table
CREATE TABLE IF NOT EXISTS post_categories (
    post_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    PRIMARY KEY (post_id, category_id),
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_published_at ON posts(published_at);
CREATE INDEX IF NOT EXISTS idx_posts_slug ON posts(slug);
CREATE INDEX IF NOT EXISTS idx_posts_author_id ON posts(author_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Insert a default author
INSERT OR IGNORE INTO users (id, username, email, display_name, bio, status, email_verified)
VALUES (1, 'author', '<EMAIL>', 'Blog Author', 'Content creator and shayari enthusiast', 'active', TRUE);

-- Insert some sample categories
INSERT OR IGNORE INTO categories (name, slug, description) VALUES
('प्रेम शायरी', 'prem-shayari', 'Love and romance poetry'),
('दुख शायरी', 'dukh-shayari', 'Sad and emotional poetry'),
('मोटिवेशनल', 'motivational', 'Inspirational and motivational content'),
('दोस्ती शायरी', 'dosti-shayari', 'Friendship poetry');

-- Insert some sample posts
INSERT OR IGNORE INTO posts (id, title, slug, content, excerpt, author_id, status, published_at) VALUES
(1, '501+ Best Good Night Shayari in Hindi – सुकून भरी शुभ रात्रि', 'good-night-shayari-hindi', 
'Good Night Shayari सिर्फ शुभ रात्रि कहने का एक तरीका नहीं, बल्कि दिल की गहराइयों से जुड़े जज़्बातों को बयां करने का poetic अंदाज़ है। जब रात का सन्नाटा फैलता है और दिल किसी खास को याद करता है, तब एक खूबसूरत शायरी आपके जज़्बातों की आवाज़ बन जाती है।',
'Good Night Shayari सिर्फ शुभ रात्रि कहने का एक तरीका नहीं, बल्कि दिल की गहराइयों से जुड़े जज़्बातों को बयां करने का poetic अंदाज़ है।',
1, 'published', '2025-01-15 18:00:00'),

(2, '501+ Best Pyar Bhari Shayari – दिल से जुड़ी मोहब्बत', 'pyar-bhari-shayari', 
'Pyar Bhari Shayari न सिर्फ़ अल्फ़ाज़ होती है, बल्कि दिल की सबसे हसीन भावनाओं को बयां करने का सबसे खूबसूरत तरीका है। जब प्यार दिल में उतरता है, तो हर शब्द में जादू होता है, हर लफ़्ज़ में इश्क़ की खुशबू होती है।',
'Pyar Bhari Shayari न सिर्फ़ अल्फ़ाज़ होती है, बल्कि दिल की सबसे हसीन भावनाओं को बयां करने का सबसे खूबसूरत तरीका है।',
1, 'published', '2025-01-14 16:30:00'),

(3, '301+ Best Miss You Yaad Shayari – अधूरी यादों की सच्ची आवाज़', 'miss-you-yaad-shayari', 
'Miss You Yaad Shayari वो खामोशी है जिसे हम लफ़्ज़ों में नहीं कह पाते, बस महसूस करते हैं। जब कोई इतना अपना हो कि उसकी गैर-मौजूदगी भी हमें अंदर से तोड़ दे, तब हर बात में उसकी याद आने लगती है।',
'Miss You Yaad Shayari वो खामोशी है जिसे हम लफ़्ज़ों में नहीं कह पाते, बस महसूस करते हैं।',
1, 'published', '2025-01-13 14:15:00'),

(4, '100+ Best Motivational Thoughts In Hindi – जरूर पढ़ें! ✅', 'motivational-thoughts-hindi', 
'मोटिवेशनल थॉट्स हिंदी (Motivational Thoughts In Hindi): यहाँ आपको 100+ Best Motivational Thoughts In Hindi मिलेंगे, जिन्हें आप फ्री में डाउनलोड कर सकते हैं। जीवन में सफलता पाने के लिए प्रेरणा बहुत जरूरी है।',
'मोटिवेशनल थॉट्स हिंदी (Motivational Thoughts In Hindi): यहाँ आपको 100+ Best Motivational Thoughts In Hindi मिलेंगे।',
1, 'published', '2025-01-12 10:00:00');
