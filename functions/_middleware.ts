// Cloudflare Pages middleware to initialize D1 database
import { db } from '../src/lib/database';

export async function onRequest(context: any) {
  // Initialize D1 database if available
  if (context.env?.DB) {
    try {
      db.setDatabase(context.env.DB);
      console.log('D1 database initialized in middleware');
    } catch (error) {
      console.error('Failed to initialize D1 database:', error);
    }
  } else {
    console.log('D1 database not available, using mock data');
  }

  // Continue to the next handler
  return context.next();
}
