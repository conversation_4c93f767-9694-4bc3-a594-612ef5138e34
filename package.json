{"name": "blog-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:wrangler": "wrangler pages dev --compatibility-date=2024-01-01 --d1=DB", "build": "next build", "start": "next start", "lint": "next lint", "db:migrate": "wrangler d1 migrations apply blog-database", "db:migrate:local": "wrangler d1 migrations apply blog-database --local", "db:shell": "wrangler d1 execute blog-database --command", "db:shell:local": "wrangler d1 execute blog-database --local --command"}, "dependencies": {"@cloudflare/next-on-pages": "^1.13.13", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "wrangler": "^4.26.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}