# Cloudflare D1 Database Setup Guide

This blog site now uses **Cloudflare D1** exclusively for data storage. All mock data functionality has been removed.

## Prerequisites

1. **Cloudflare Account**: You need a Cloudflare account with Workers/Pages access
2. **Wrangler CLI**: Install and authenticate with <PERSON>rang<PERSON>
   ```bash
   npm install -g wrangler
   wrangler auth login
   ```

## Database Setup

### 1. Create D1 Database

```bash
# Create a new D1 database
wrangler d1 create blog-database

# This will output something like:
# database_id = "38d675e4-df5b-4c8a-b6f2-490996d8dd89"
```

### 2. Update Configuration

Update `wrangler.toml` with your database ID:

```toml
[[d1_databases]]
binding = "DB"
database_name = "blog-database"
database_id = "YOUR_DATABASE_ID_HERE"
```

### 3. Run Database Migrations

```bash
# Apply migrations to production database
npm run db:migrate

# For local development
npm run db:migrate:local
```

### 4. Initialize Database with Sample Data

The migration file `migrations/0001_initial_schema.sql` includes:
- Database schema (users, posts, categories tables)
- Sample author user
- Sample categories
- Sample blog posts

## Development

### Local Development with D1

```bash
# Start development server with D1 local database
npm run dev:wrangler

# Or use regular Next.js dev (will show database errors until D1 is connected)
npm run dev
```

### Database Commands

```bash
# Execute SQL commands on production database
npm run db:shell "SELECT * FROM posts;"

# Execute SQL commands on local database
npm run db:shell:local "SELECT * FROM posts;"
```

## Production Deployment

### Cloudflare Pages

1. Connect your repository to Cloudflare Pages
2. Set build command: `npm run build`
3. Set output directory: `.next`
4. Add D1 database binding in Pages settings:
   - Variable name: `DB`
   - D1 database: Select your `blog-database`

### Environment Variables

The `.env.local` file contains:
```env
CLOUDFLARE_D1_DATABASE_ID=your-database-id
CLOUDFLARE_D1_DATABASE_NAME=blog-database
```

## Database Schema

### Tables

- **users**: Blog authors and user accounts
- **posts**: Blog posts with metadata
- **categories**: Post categories
- **post_categories**: Many-to-many relationship between posts and categories

### Key Features

- Proper foreign key relationships
- Indexes for performance
- Default values and constraints
- Sample data for immediate testing

## Error Handling

The application now includes proper error handling:

- **Database Connection Errors**: Shows user-friendly error messages
- **Missing Posts**: Returns 404 for non-existent posts
- **Build-time Errors**: Gracefully handles database unavailability during build

## Troubleshooting

### Common Issues

1. **"Database not initialized" error**:
   - Ensure D1 database is created and configured in `wrangler.toml`
   - Run migrations: `npm run db:migrate`

2. **Build fails with database errors**:
   - This is expected during static generation
   - The app will work correctly at runtime with proper D1 binding

3. **Local development shows errors**:
   - Use `npm run dev:wrangler` instead of `npm run dev`
   - Ensure local D1 database is set up with migrations

### Useful Commands

```bash
# Check D1 databases
wrangler d1 list

# Backup database
wrangler d1 export blog-database --output backup.sql

# Import data
wrangler d1 execute blog-database --file backup.sql
```

## Migration from Mock Data

All mock data functionality has been completely removed:
- ✅ No more `getMockData()` or `getMockPosts()` methods
- ✅ No hardcoded post arrays
- ✅ No mock user fallbacks
- ✅ Proper error handling for database failures
- ✅ Production-ready D1 integration

The application now requires a real Cloudflare D1 database connection to function properly.
