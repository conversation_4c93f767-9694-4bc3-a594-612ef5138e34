import { Post, User } from './types';

interface DatabaseResult {
  results?: unknown[];
}

interface DatabaseStatement {
  bind(...params: unknown[]): DatabaseStatement;
  all(): Promise<DatabaseResult>;
}

export interface Database {
  prepare(query: string): DatabaseStatement;
}

// Mock data for development when D1 is not available
const mockPosts: Post[] = [
  {
    id: 1,
    title: '501+ Best Good Night Shayari in Hindi – सुकून भरी शुभ रात्रि',
    slug: 'good-night-shayari-hindi',
    content: 'Good Night Shayari सिर्फ शुभ रात्रि कहने का एक तरीका नहीं, बल्कि दिल की गहराइयों से जुड़े जज़्बातों को बयां करने का poetic अंदाज़ है। जब रात का सन्नाटा फैलता है और दिल किसी खास को याद करता है, तब एक खूबसूरत शायरी आपके जज़्बातों की आवाज़ बन जाती है।',
    excerpt: 'Good Night Shayari सिर्फ शुभ रात्रि कहने का एक तरीका नहीं, बल्कि दिल की गहराइयों से जुड़े जज़्बातों को बयां करने का poetic अंदाज़ है।',
    author_id: 1,
    status: 'published',
    post_type: 'post',
    featured_image_url: null,
    meta_title: null,
    meta_description: null,
    language: 'hi',
    view_count: 0,
    comment_count: 0,
    published_at: '2025-01-15T18:00:00Z',
    created_at: '2025-01-15T18:00:00Z',
    updated_at: '2025-01-15T18:00:00Z'
  },
  {
    id: 2,
    title: '501+ Best Pyar Bhari Shayari – दिल से जुड़ी मोहब्बत',
    slug: 'pyar-bhari-shayari',
    content: 'Pyar Bhari Shayari न सिर्फ़ अल्फ़ाज़ होती है, बल्कि दिल की सबसे हसीन भावनाओं को बयां करने का सबसे खूबसूरत तरीका है। जब प्यार दिल में उतरता है, तो हर शब्द में जादू होता है, हर लफ़्ज़ में इश्क़ की खुशबू होती है।',
    excerpt: 'Pyar Bhari Shayari न सिर्फ़ अल्फ़ाज़ होती है, बल्कि दिल की सबसे हसीन भावनाओं को बयां करने का सबसे खूबसूरत तरीका है।',
    author_id: 1,
    status: 'published',
    post_type: 'post',
    featured_image_url: null,
    meta_title: null,
    meta_description: null,
    language: 'hi',
    view_count: 0,
    comment_count: 0,
    published_at: '2025-01-14T16:30:00Z',
    created_at: '2025-01-14T16:30:00Z',
    updated_at: '2025-01-14T16:30:00Z'
  },
  {
    id: 3,
    title: '301+ Best Miss You Yaad Shayari – अधूरी यादों की सच्ची आवाज़',
    slug: 'miss-you-yaad-shayari',
    content: 'Miss You Yaad Shayari वो खामोशी है जिसे हम लफ़्ज़ों में नहीं कह पाते, बस महसूस करते हैं। जब कोई इतना अपना हो कि उसकी गैर-मौजूदगी भी हमें अंदर से तोड़ दे, तब हर बात में उसकी याद आने लगती है।',
    excerpt: 'Miss You Yaad Shayari वो खामोशी है जिसे हम लफ़्ज़ों में नहीं कह पाते, बस महसूस करते हैं।',
    author_id: 1,
    status: 'published',
    post_type: 'post',
    featured_image_url: null,
    meta_title: null,
    meta_description: null,
    language: 'hi',
    view_count: 0,
    comment_count: 0,
    published_at: '2025-01-13T14:15:00Z',
    created_at: '2025-01-13T14:15:00Z',
    updated_at: '2025-01-13T14:15:00Z'
  }
];

const mockUser: User = {
  id: 1,
  username: 'author',
  email: '<EMAIL>',
  display_name: 'Blog Author',
  bio: 'Content creator and shayari enthusiast',
  avatar_url: null,
  status: 'active',
  email_verified: true,
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z'
};

// Database service that works with both Cloudflare D1 and fallback mock data
export class DatabaseService {
  private static instance: DatabaseService;
  private db: Database | null = null;
  private isD1Available = false;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Initialize Cloudflare D1 database connection
  public setDatabase(database: Database) {
    this.db = database;
    this.isD1Available = true;
  }

  // Execute query - works with Cloudflare D1 or falls back to mock data
  private async executeQuery(query: string, params: unknown[] = []): Promise<DatabaseResult> {
    if (!this.db || !this.isD1Available) {
      console.warn('D1 database not available, using mock data for development');
      // Return empty results for non-D1 environment
      return { results: [] };
    }

    try {
      const stmt = this.db.prepare(query);
      if (params.length > 0) {
        return await stmt.bind(...params).all();
      }
      return await stmt.all();
    } catch (error) {
      console.error('Database query failed, falling back to mock data:', error);
      // Fall back to mock data on error
      return { results: [] };
    }
  }



  async getAllPosts(): Promise<Post[]> {
    if (!this.isD1Available) {
      console.log('Using mock data for getAllPosts');
      return mockPosts;
    }

    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE status = 'published'
      ORDER BY published_at DESC
    `;

    try {
      const result = await this.executeQuery(query);
      return (result.results as Post[]) || mockPosts;
    } catch (error) {
      console.warn('D1 query failed, using mock data:', error);
      return mockPosts;
    }
  }

  async getPostBySlug(slug: string): Promise<Post | null> {
    if (!this.isD1Available) {
      console.log('Using mock data for getPostBySlug');
      return mockPosts.find(post => post.slug === slug) || null;
    }

    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE slug = ? AND status = 'published'
    `;

    try {
      const result = await this.executeQuery(query, [slug]);
      return (result.results?.[0] as Post) || mockPosts.find(post => post.slug === slug) || null;
    } catch (error) {
      console.warn('D1 query failed, using mock data:', error);
      return mockPosts.find(post => post.slug === slug) || null;
    }
  }

  async getPostById(id: number): Promise<Post | null> {
    if (!this.isD1Available) {
      console.log('Using mock data for getPostById');
      return mockPosts.find(post => post.id === id) || null;
    }

    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE id = ? AND status = 'published'
    `;

    try {
      const result = await this.executeQuery(query, [id]);
      return (result.results?.[0] as Post) || mockPosts.find(post => post.id === id) || null;
    } catch (error) {
      console.warn('D1 query failed, using mock data:', error);
      return mockPosts.find(post => post.id === id) || null;
    }
  }

  async getUserById(id: number): Promise<User | null> {
    if (!this.isD1Available) {
      console.log('Using mock data for getUserById');
      return id === 1 ? mockUser : null;
    }

    const query = `
      SELECT id, username, email, display_name, bio, avatar_url, status,
             email_verified, created_at, updated_at
      FROM users
      WHERE id = ?
    `;

    try {
      const result = await this.executeQuery(query, [id]);
      return (result.results?.[0] as User) || (id === 1 ? mockUser : null);
    } catch (error) {
      console.warn('D1 query failed, using mock data:', error);
      return id === 1 ? mockUser : null;
    }
  }
}

export const db = DatabaseService.getInstance();
