import { Post, User } from './types';

interface DatabaseResult {
  results?: unknown[];
}

interface DatabaseStatement {
  bind(...params: unknown[]): DatabaseStatement;
  all(): Promise<DatabaseResult>;
}

export interface Database {
  prepare(query: string): DatabaseStatement;
}

// Database service that works exclusively with Cloudflare D1
export class DatabaseService {
  private static instance: DatabaseService;
  private db: Database | null = null;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Initialize Cloudflare D1 database connection
  public setDatabase(database: Database) {
    this.db = database;
  }

  // Execute query - works exclusively with Cloudflare D1
  private async executeQuery(query: string, params: unknown[] = []): Promise<DatabaseResult> {
    if (!this.db) {
      throw new Error('Database not initialized. Please ensure Cloudflare D1 database is properly configured.');
    }

    try {
      const stmt = this.db.prepare(query);
      if (params.length > 0) {
        return await stmt.bind(...params).all();
      }
      return await stmt.all();
    } catch (error) {
      console.error('Database query failed:', error);
      throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }



  async getAllPosts(): Promise<Post[]> {
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE status = 'published'
      ORDER BY published_at DESC
    `;
    const result = await this.executeQuery(query);
    return (result.results as Post[]) || [];
  }

  async getPostBySlug(slug: string): Promise<Post | null> {
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE slug = ? AND status = 'published'
    `;
    const result = await this.executeQuery(query, [slug]);
    return (result.results?.[0] as Post) || null;
  }

  async getPostById(id: number): Promise<Post | null> {
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE id = ? AND status = 'published'
    `;
    const result = await this.executeQuery(query, [id]);
    return (result.results?.[0] as Post) || null;
  }

  async getUserById(id: number): Promise<User | null> {
    const query = `
      SELECT id, username, email, display_name, bio, avatar_url, status,
             email_verified, created_at, updated_at
      FROM users
      WHERE id = ?
    `;
    const result = await this.executeQuery(query, [id]);
    return (result.results?.[0] as User) || null;
  }
}

export const db = DatabaseService.getInstance();
