// D1 Database initialization for Cloudflare Workers/Pages
import { db } from './database';

// This function should be called in your Cloudflare Worker/Pages function
// to initialize the database connection with the D1 binding
export function initializeDatabase(env: { DB: D1Database }) {
  if (!env.DB) {
    throw new Error('D1 database binding "DB" not found. Please check your wrangler.toml configuration.');
  }
  
  // Set the D1 database instance
  db.setDatabase(env.DB as unknown as import('./database').Database);
  
  console.log('D1 database initialized successfully');
}

// Type definition for Cloudflare D1 Database
declare global {
  interface D1Database {
    prepare(query: string): D1PreparedStatement;
    dump(): Promise<ArrayBuffer>;
    batch<T = unknown>(statements: D1PreparedStatement[]): Promise<D1Result<T>[]>;
    exec(query: string): Promise<D1ExecResult>;
  }

  interface D1PreparedStatement {
    bind(...values: unknown[]): D1PreparedStatement;
    first<T = unknown>(colName?: string): Promise<T | null>;
    run(): Promise<D1Result>;
    all<T = unknown>(): Promise<D1Result<T>>;
    raw<T = unknown[]>(): Promise<T[]>;
  }

  interface D1Result<T = Record<string, unknown>> {
    results?: T[];
    success: boolean;
    error?: string;
    meta: {
      duration: number;
      size_after: number;
      rows_read: number;
      rows_written: number;
    };
  }

  interface D1ExecResult {
    count: number;
    duration: number;
  }
}

export type { D1Database, D1PreparedStatement, D1Result, D1ExecResult };
