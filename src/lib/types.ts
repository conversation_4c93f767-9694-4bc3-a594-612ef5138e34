export interface Post {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string | null;
  author_id: number;
  status: string;
  post_type: string;
  featured_image_url: string | null;
  meta_title: string | null;
  meta_description: string | null;
  language: string;
  view_count: number;
  comment_count: number;
  published_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  display_name: string;
  bio: string | null;
  avatar_url: string | null;
  status: string;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  parent_id: number | null;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface PostWithAuthor extends Post {
  author: User;
}

export interface PostWithCategories extends PostWithAuthor {
  categories: Category[];
}
