import type { Metadata } from "next";
import { Inter, Noto_Sans_Devanagari } from 'next/font/google';

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-family-base",
});

const noto_sans_devanagari = Noto_Sans_Devanagari({
  subsets: ["latin"],
  variable: "--font-family-hindi",
});

export const metadata: Metadata = {
  title: "शायरी ब्लॉग - Hindi Poetry Blog",
  description: "प्रेम, दुख, खुशी की अनमोल शायरियाँ",
};

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <html lang="hi">
      <body
        className={`${inter.variable} ${noto_sans_devanagari.variable} antialiased`}
      >
        <header className="header">
          <div className="container">
            <div className="flex items-center justify-between">
              <div className="logo">
                <h1 className="logo-text">शायरी ब्लॉग</h1>
              </div>
              <nav className="nav-menu">
                <ul className="nav-list">
                  <li>
                    <a href="#" className="nav-link">
                      Home
                    </a>
                  </li>
                  <li>
                    <a href="#" className="nav-link">
                      श्रेणियाँ
                    </a>
                  </li>
                  <li>
                    <a href="#" className="nav-link">
                      लेखक
                    </a>
                  </li>
                  <li>
                    <a href="#" className="nav-link">
                      हमारे बारे में
                    </a>
                  </li>
                </ul>
              </nav>
              <div className="header-actions flex items-center gap-16">
                <div className="search-container">
                  <input
                    type="text"
                    className="search-input"
                    placeholder="शायरी खोजें..."
                  />
                  <button className="search-btn">🔍</button>
                </div>
                <button className="lang-toggle btn btn--outline btn--sm">
                  हिंदी/En
                </button>
              </div>
            </div>
          </div>
        </header>

        {children}

        <footer className="footer">
          <div className="container">
            <div className="footer-content">
              <div className="footer-section">
                <h4 className="footer-title">श्रेणियाँ</h4>
                <ul className="footer-links">
                  <li>
                    <a href="#">प्रेम शायरी</a>
                  </li>
                  <li>
                    <a href="#">दुख शायरी</a>
                  </li>
                  <li>
                    <a href="#">मोटिवेशनल शायरी</a>
                  </li>
                  <li>
                    <a href="#">दोस्ती शायरी</a>
                  </li>
                </ul>
              </div>

              <div className="footer-section">
                <h4 className="footer-title">लेखक</h4>
                <ul className="footer-links">
                  <li>
                    <a href="#">राहुल शर्मा</a>
                  </li>
                  <li>
                    <a href="#">प्रिया गुप्ता</a>
                  </li>
                  <li>
                    <a href="#">अमित कुमार</a>
                  </li>
                  <li>
                    <a href="#">सुनीता देवी</a>
                  </li>
                </ul>
              </div>

              <div className="footer-section">
                <h4 className="footer-title">About</h4>
                <ul className="footer-links">
                  <li>
                    <a href="#">हमारे बारे में</a>
                  </li>
                  <li>
                    <a href="#">Contact</a>
                  </li>
                  <li>
                    <a href="#">Privacy Policy</a>
                  </li>
                  <li>
                    <a href="#">Terms of Service</a>
                  </li>
                </ul>
              </div>

              <div className="footer-section">
                <h4 className="footer-title">Follow Us</h4>
                <div className="social-links">
                  <a href="#" className="social-link">
                    📘 Facebook
                  </a>
                  <a href="#" className="social-link">
                    🐦 Twitter
                  </a>
                  <a href="#" className="social-link">
                    📷 Instagram
                  </a>
                  <a href="#" className="social-link">
                    📺 YouTube
                  </a>
                </div>
              </div>
            </div>

            <div className="footer-bottom">
              <p>&copy; 2025 शायरी ब्लॉग. All rights reserved.</p>
              <p>Contact: <EMAIL> | +91 9876543210</p>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}
