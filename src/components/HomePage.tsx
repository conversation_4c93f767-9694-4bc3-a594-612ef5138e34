import { db } from '@/lib/database';
import { Post } from '@/lib/types';
import ShayariCard from './ShayariCard';

export default async function HomePage() {
  let posts: Post[] = [];
  let error: string | null = null;

  try {
    posts = await db.getAllPosts();
  } catch (err) {
    console.error('Failed to fetch posts:', err);
    error = err instanceof Error ? err.message : 'Failed to load posts';
    posts = [];
  }

  return (
    <>
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-banner">
              <div className="hero-image-placeholder"></div>
              <div className="hero-text">
                <h2 className="hero-title">आज की खास शायरी</h2>
                <p className="hero-subtitle">प्रेम, दुख, खुशी की अनमोल शायरियाँ</p>
                <button className="btn btn--primary">Read More</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <main className="main-content">
        <div className="container">
          <div className="content-layout">
            <div className="shayari-grid">
              <h3 className="section-title">Latest शायरी</h3>

              {error ? (
                <div className="error-state">
                  <h3>डेटाबेस कनेक्शन में समस्या</h3>
                  <p>कृपया बाद में फिर से कोशिश करें।</p>
                  <details className="error-details">
                    <summary>Technical Details</summary>
                    <p>{error}</p>
                  </details>
                </div>
              ) : posts.length === 0 ? (
                <div className="no-results">
                  <h3>कोई शायरी नहीं मिली</h3>
                  <p>कृपया बाद में फिर से देखें।</p>
                </div>
              ) : (
                <div className="cards-grid" id="shayariGrid">
                  {posts.map((post) => (
                    <ShayariCard key={post.id} post={post} />
                  ))}
                </div>
              )}
              
              <div className="load-more-container">
                <button className="btn btn--outline">Load More शायरी</button>
              </div>
            </div>

            <aside className="sidebar">
              <div className="widget">
                <h4 className="widget-title">Popular शायरी</h4>
                <div className="popular-list">
                  {/* This can be made dynamic later */}
                </div>
              </div>

              <div className="widget">
                <h4 className="widget-title">श्रेणियाँ</h4>
                <div className="categories-list" id="categoriesList">
                  {/* This can be made dynamic later */}
                </div>
              </div>

              <div className="widget">
                <h4 className="widget-title">Recent Posts</h4>
                <div className="recent-list">
                  {/* This can be made dynamic later */}
                </div>
              </div>

              <div className="widget">
                <h4 className="widget-title">Author Spotlight</h4>
                <div className="author-spotlight">
                  {/* This can be made dynamic later */}
                </div>
              </div>

              <div className="widget">
                <h4 className="widget-title">Newsletter</h4>
                <div className="newsletter-signup">
                  <p>रोज़ाना नई शायरी पाएं</p>
                  <input type="email" className="form-control" placeholder="Your email" />
                  <button className="btn btn--primary btn--full-width mt-8">Subscribe</button>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </main>
    </>
  );
}
